<template>
  <div class="home-container">
    <div class="home-main">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-card header="我的待办">
            <div v-for="item in todoList" :key="item.id" class="todo-item">
              <el-tag type="info">未完成</el-tag>
              <span class="title">{{ item.title }}</span>
            </div>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card header="今日任务">
            <div v-for="task in tasks" :key="task.id" class="task-item">
              <el-icon><alarm-clock /></el-icon>
              <span class="time">{{ task.time }}</span>
              <span class="title">{{ task.title }}</span>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup>
import { computed , onMounted} from 'vue'
import { useUserStore } from '@/store/home/<USER>'
import { removeToken } from '@/utils/auth'

const userStore = useUserStore()

// 添加空值保护
const todoList = computed(() => userStore.userInfo?.todoList || [])
const tasks = computed(() => userStore.userInfo?.tasks || [])

function testRemoveToken(){
  removeToken()
}

// 初始化用户数据
onMounted(() => {
  if (!userStore.userInfo.todoList) {
    userStore.initUserInfo()
  }
})

// testRemoveToken()
</script>

<style scoped>
.home-container {
  display: flex;
  height: 100%;
}

.home-main {
  flex: 1;
  padding: 20px;
}

.todo-item, .task-item {
  display: flex;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px solid #eee;
}

.todo-item .title {
  margin-left: 15px;
}

.task-item .time {
  margin: 0 15px;
  color: #666;
}
</style>