<template>
  <el-dialog
    :title="title"
    :model-value="open"
    align-center
    width="820px"
    append-to-body
    @close="cancel"
  >
    <el-form
      :model="form"
      :rules="type === 'view' ? {} : rules"
      ref="siteRef"
      label-width="120px"
      class="el-form--inline"
      :inline="true"
      :disabled="type === 'view'"
    >
      <el-form-item label="规则名称" prop="ruleName">
        <el-input
          v-model="form.ruleName"
          placeholder="请输入规则名称"
          maxlength="30"
        />
      </el-form-item>
      <common-form
        v-model:form="form"
        :editRecord="editRecord"
        :isSiteNameSingle="true"
        :disabled="type === 'edit'"
      ></common-form>
      <el-form-item label="监测活动大类" prop="activityType">
        <el-select
          v-model="form.activityType"
          value-key="id"
          placeholder="请选择监测活动大类"
          :disabled="type === 'edit'"
        >
          <el-option
            v-for="dict in activityParentTypeOptions"
            :key="dict.activityTypeCode"
            :label="dict.activityTypeName"
            :value="dict.activityTypeCode"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="监测活动小类" prop="activitySubtype">
        <el-select
          v-model="form.activitySubtype"
          placeholder="请选择监测活动小类"
          :disabled="type === 'edit'"
          clearable
        >
          <el-option
            v-for="dict in activityTypeOptions"
            :key="dict.activitySubtypeCode"
            :label="dict.activitySubtypeName"
            :value="dict.activitySubtypeCode"
          />
        </el-select>
      </el-form-item>
      <!-- <el-form-item label="是否有伴生规则" prop="isAssoplan">
        <el-select
          v-model="form.isAssoplan"
          placeholder="请选择是否有伴生规则"
          clearable
          disabled
        >
          <el-option
            v-for="dict in yesOrNo"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item> -->
      <!-- <el-form-item label="伴生规则" prop="assoplanRule">
        <el-select
          v-model="form.assoplanRule"
          placeholder="请选择伴生规则"
          clearable
          :disabled="form.isAssoplan != '1'"
          multiple
        >
          <el-option
            v-for="dict in sampleType"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item> -->

      <!-- <el-form-item label="规则编码" prop="ruleCode">
        <el-input
          v-model="form.ruleCode"
          placeholder="请输入目标值"
          maxlength="30"
        />
      </el-form-item> -->

      <!-- <el-form-item label="计划生成周期" prop="planCronRule">
        <el-select
          v-model="form.planCronRule"
          placeholder="请选择规则编码"
          clearable
          :multiple="!form.id"
          disabled
        >
          <el-option
            v-for="dict in siteOptions"
            :key="dict.siteId"
            :label="dict.siteName"
            :value="dict.siteId"
          />
        </el-select>
      </el-form-item> -->
      <el-form-item label="是否启用" prop="ruleStatus" v-if="type !== 'add'">
        <el-switch
          v-model="form.ruleStatus"
          :active-value="1"
          :inactive-value="0"
        ></el-switch>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="cancel">取 消</el-button>
        <!-- <el-button
          type="primary"
          v-if="type !== 'view'"
          :loading="loadingBtnSave"
          @click="submitForm(0)"
          >保 存</el-button
        > -->
        <el-button
          type="primary"
          v-if="type !== 'view'"
          :loading="loadingBtnSubmit"
          @click="submitForm(1)"
          >提 交</el-button
        >
      </div>
    </template>
  </el-dialog>
</template>

<script setup name="User">
import { savePlanRule, batchSavePlanRule, updatePlanRule, qryAssoPlanRule } from "@/api/smartschedu/plan";
import {
  getActivityParentType,
  getActivityType,
} from "@/api/smartschedu/common";
import { nextTick, watch } from "vue";
import commonForm from "../../component/commonForm.vue";
import { yesOrNo, sampleType } from "../../common/optionsData";

const { proxy } = getCurrentInstance();

const emit = defineEmits(["update:open"]);
const props = defineProps({
  open: {
    type: Boolean,
    default: false,
  },
  getList: {
    type: Function,
    default: () => {},
  },
  title: {
    type: String,
    default: "",
  },
  type: {
    type: String,
    default: "",
  },
  editRecord: {
    type: Object,
    default: () => {},
  },
});
const loadingBtnSave = ref(false);
const loadingBtnSubmit = ref(false);

const activityTypeOptions = ref(undefined);
const activityParentTypeOptions = ref(undefined);
const siteOptions = ref(undefined);

const data = reactive({
  form: {
    ruleStatus: "1",
  },
  queryParams: {
    pageNo: 1,
    pageSize: 10,
    cityCode: undefined,
    siteName: undefined,
    siteId: undefined,
    businessType: "water",
    siteType: undefined,
    activitySubtype: undefined,
    activityMajorType: undefined,
  },
  rules: {
    siteType: [
      {
        required: true,
        message: "请选择站点类型",
        trigger: ["blur", "change"],
      },
    ],
    isAssoplan: [
      {
        required: true,
        message: "请选择是否有伴生规则",
        trigger: ["blur", "change"],
      },
    ],
    ruleName: [
      {
        required: true,
        message: "请输入规则名称",
        trigger: ["blur", "change"],
      },
    ],
    businessType: [
      {
        required: true,
        message: "请选择业务类型",
        trigger: ["blur", "change"],
      },
    ],
    activityType: [
      {
        required: true,
        message: "请选择监测活动大类",
        trigger: ["blur", "change"],
      },
    ],
    // activitySubtype: [
    //   {
    //     required: true,
    //     message: "请选择监测活动小类",
    //     trigger: ["blur", "change"],
    //   },
    // ],
    targetValue: [{ required: true, message: "请输入目标值", trigger: "blur" }],
    provinceCode: [
      {
        required: true,
        message: "请选择省份",
        trigger: ["blur", "change"],
      },
    ],
    // cityCode: [
    //   {
    //     required: true,
    //     message: "请选择地市",
    //     trigger: ["blur", "change"],
    //   },
    // ],
    // siteId: [
    //   {
    //     required: true,
    //     message: "请选择站点名称",
    //     trigger: ["blur", "change"],
    //   },
    // ],
    ruleStatus: [
      {
        required: true,
        message: "请选择启用状态",
        trigger: ["blur", "change"],
      },
    ],
  },
});

const { queryParams, form, rules } = toRefs(data);

watch(
  () => props.editRecord,
  (newVal, oldVal) => {
    if (newVal) {
      initData(newVal);
    }
  }
);

watch(
  [
    () => form.value.businessType,
    () => form.value.siteType,
    () => form.value.activityType,
    () => form.value.cityCode,
    () => form.value.provinceCode,
    () => form.value.isAutosite,
  ],
  (
    [
      newBusinessType,
      newSiteType,
      newActivityType,
      newCityCode,
      newProvinceCode,
      newIsAutosite,
    ],
    [
      oldBusinessType,
      oldSiteType,
      oldActivityType,
      oldCityCode,
      oldProvinceCode,
      oldIsAutosite,
    ]
  ) => {
    if (form.value.isInit) {
      form.value.isInit = false;
      return;
    }

    // 重置监测类型大类与小类并且获取监测大类列表项
    if (newBusinessType !== oldBusinessType || newSiteType !== oldSiteType) {
      if (newSiteType !== oldSiteType) {
        // 站点类型 水断面  -100 和 国控站点 01 为同一个, 如果新的为数字，老的为字符串，则认为是初始化过程，无需重置
        if (
          (newSiteType === -100 && oldSiteType === "01") ||
          (newSiteType === "01" && oldSiteType === -100) ||
          (typeof newSiteType === "number" && typeof oldSiteType === "string")
        ) {
          return;
        }
        form.value.activityType = undefined;
        form.value.activitySubtype = undefined;
        form.value.assoplanRule = undefined;
        form.value.isAssoplan = "0";

        getActivityParentTypeList(
          newBusinessType,
          form.value?.siteTypeAlias,
          form.value?.isAutosite
        );
      } else {
        form.value.activityType = undefined;
        form.value.activitySubtype = undefined;
        form.value.assoplanRule = undefined;
        form.value.isAssoplan = "0";

        getActivityParentTypeList(
          newBusinessType,
          form.value?.siteTypeAlias,
          form.value?.isAutosite
        );
      }
    }
    // 重置监测类型子类与获取子类列表项
    if (
      newBusinessType !== oldBusinessType ||
      newSiteType !== oldSiteType ||
      newActivityType !== oldActivityType
    ) {
      form.value.activitySubtype = undefined;
      // 如果监测类型大类为 断面采样 则默认为有伴生规则,否则为无伴生规则
      if (newActivityType !== oldActivityType) {
        form.value.isAssoplan =
          newActivityType === "section_sample" ? "1" : "0";

        // 根据活动大类设置是否自动站逻辑
        const selectedActivityType = activityParentTypeOptions.value?.find(
          (item) => item.activityTypeCode === newActivityType
        );
        // 0不是自动站1是自动站2都行
        // if (selectedActivityType?.isAutosite) {
        //   if (
        //     form.value.isAutositeActivity !== selectedActivityType?.isAutosite
        //   ) {
        //     form.value.siteId = undefined;
        //   }
        //   form.value.isAutositeActivity = selectedActivityType?.isAutosite;
        // }
      }

      getActivityList(
        newActivityType,
        newBusinessType,
        form.value?.siteTypeAlias
      );
    }
    // 重置城市、站点名称
    if (
      newCityCode !== oldCityCode ||
      newProvinceCode !== oldProvinceCode ||
      newSiteType !== oldSiteType
    ) {
      if (newProvinceCode !== oldProvinceCode) {
        form.value.cityCode = undefined;
      }
      if (newSiteType !== oldSiteType) {
        // 站点类型 水断面  -100 和 国控站点 01 为同一个, 如果新的为数字，老的为字符串，则认为是初始化过程，无需重置
        if (
          (newSiteType === -100 && oldSiteType === "01") ||
          (newSiteType === "01" && oldSiteType === -100) ||
          (typeof newSiteType === "number" && typeof oldSiteType === "string")
        ) {
          return;
        }
      }
      form.value.siteId = undefined;
    }
  },
  { deep: true }
);

function initData(newVal) {
  nextTick(() => {
    form.value = {
      ...newVal,
      isAssoplan: newVal?.isAssoplan ? `${newVal.isAssoplan}` : "0",
      isInit: true,
    };
  });
  getAssoPlanRule(newVal.id);

  getActivityList(newVal.activityType, newVal.businessType, newVal.siteType);
  getActivityParentTypeList(newVal.businessType, newVal.siteType);
}

function getActivityList(activityTypeCode, businessType, siteType) {
  getActivityType({
    activityTypeCode,
    businessType,
    siteType,
    isRegular: 1,
  }).then((response) => {
    activityTypeOptions.value = response.data;
  });
}
function getActivityParentTypeList(businessType, siteType, isAutosite) {
  const obj = {
    businessType,
    siteType,
    isRegular: 1,
  };
  if (businessType === "water") {
    obj.isAutosite = isAutosite;
  }
  getActivityParentType(obj).then((response) => {
    activityParentTypeOptions.value = response.data;
  });
}

/** 重置操作表单 */
function reset() {
  form.value = {
    provinceCode: undefined,
    provinceName: undefined,
    cityCode: undefined,
    cityName: undefined,
    siteName: undefined,
    siteId: undefined,
    siteType: undefined,
    businessType: "water",
    activityType: undefined,
    activitySubtype: undefined,
    targetValue: undefined,
    ruleStatus: 1,
  };
  proxy.resetForm("siteRef");
}

/** 取消按钮 */
function cancel() {
  reset();
  emit("update:open", false);
}

/** 提交按钮 */
function submitForm(submitFlag) {
  proxy.$refs["siteRef"].validate((valid) => {
    if (valid) {
      if (submitFlag === 1) {
        loadingBtnSubmit.value = true;
      } else {
        loadingBtnSave.value = true;
      }
      const assoplanRule = [];
      sampleType?.map((item) => {
        if (form.value?.assoplanRule?.indexOf(item.value) > -1) {
          assoplanRule.push({
            name: item.label,
            code: item.value,
          });
        }
      });

      // 如果是水断面，则isAutosite为0,如果选则了站点，则根据站点的类型判断，如果没有则根据选中的活动大类判断
      const isAutosite =
        form.value.siteType === -100
          ? "0"
          : form.value.isAutosite || form.value.isAutositeActivity;

      const requestObj = {
        ...form.value,
        siteType: form.value?.siteTypeAlias,
        assoplanRule,
        submitFlag,
      };
      if(props.type === 'add') {
        requestObj.ruleStatus = '1'
      }
      // 业务类型为水才会传isAutosite
      if (form.value.businessType === "water") {
        requestObj.isAutosite = isAutosite;
      }

      const savaHandle = props.type === 'add' ? batchSavePlanRule : savePlanRule ;

      savaHandle(requestObj)
        .then((response) => {
          proxy.$modal.msgSuccess(`${props.type === 'add' ? '新增' : '修改'}成功`);
          emit("update:open", false);
          props?.getList();
          if (submitFlag === 1) {
            loadingBtnSubmit.value = false;
          } else {
            loadingBtnSave.value = false;
          }
        })
        .catch(() => {
          if (submitFlag === 1) {
            loadingBtnSubmit.value = false;
          } else {
            loadingBtnSave.value = false;
          }
        });
    }
  });
}

function getAssoPlanRule(id) {
  if (!id) {
    return;
  }
  qryAssoPlanRule(id).then((response) => {
    const ids = response.data.map((item) => item.id);
    form.value.assoplanRule = ids;
  });
}

initData(props.editRecord);
</script>

<style scoped lang="scss">
.head-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
:deep(.custom-tabs) {
  .el-tabs__header {
    display: flex;
    width: 100%;
  }
  .el-tabs__item {
    flex: 1;
    text-align: center;
  }
  .el-tabs__nav {
    width: 100%;
  }
}
.top-right-btn {
  margin-left: unset;
}
</style>
