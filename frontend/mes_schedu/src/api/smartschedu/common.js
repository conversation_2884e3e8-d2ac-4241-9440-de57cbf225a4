import request from "@/utils/request";

// 获取区域树
export function getAreaTreeInfo(query) {
  return request({
    url:
      import.meta.env.VITE_REQUEST_PREFIX +
      "/rocketapi/schedu/genericmgr/getAreaTreeInfo",
    method: "get",
    params: query,
  });
}
// 获取区域树
export function getSimAreaTreeInfo(query) {
  return request({
    url:
      import.meta.env.VITE_REQUEST_PREFIX +
      "/rocketapi/schedu/genericmgr/getSimAreaTreeInfo",
    method: "get",
    params: query,
  });
}
// 获取区域树 区域
export function getRegionProvinceTreeInfo(query) {
  return request({
    url:
      import.meta.env.VITE_REQUEST_HIGH_PREFIX +
      "/genericmgr/getRegionProvinceTreeInfo",
    method: "get",
    params: query,
  });
}

// 获取片区区域树 区域
export function getPackageProvinceTreeInfo(query) {
  return request({
    url:
      import.meta.env.VITE_REQUEST_HIGH_PREFIX +
      "/genericmgr/getPackageProvinceTreeInfo",
    method: "get",
    params: query,
  });
}

// 获取省份信息
export function getProvinceInfo(query) {
  return request({
    url:
      import.meta.env.VITE_REQUEST_PREFIX +
      "/rocketapi/schedu/genericmgr/getProvinceInfo",
    method: "get",
    params: query,
  });
}
// 获取市信息
export function getCityInfo(query) {
  return request({
    url:
      import.meta.env.VITE_REQUEST_PREFIX +
      "/rocketapi/schedu/genericmgr/getCityInfo",
    method: "get",
    params: query,
  });
}
// 获取业务类型
export function getBusinessType(query) {
  return request({
    url:
      import.meta.env.VITE_REQUEST_PREFIX +
      "/rocketapi/schedu/genericmgr/getBusinessType",
    method: "get",
    params: query,
  });
}

// 获取站点类型
export function getSiteType(query) {
  return request({
    url:
      import.meta.env.VITE_REQUEST_PREFIX +
      "/rocketapi/schedu/genericmgr/getSiteType",
    method: "get",
    params: query,
  });
}
// 获取站点信息
export function getSiteInfo(query) {
  return request({
    url: import.meta.env.VITE_REQUEST_HIGH_PREFIX + "/genericmgr/getSiteInfo",
    method: "get",
    params: query,
  });
}
// 获取监测活动大类
export function getActivityParentType(query) {
  return request({
    url:
      import.meta.env.VITE_REQUEST_PREFIX +
      "/rocketapi/schedu/genericmgr/getActivityParentType",
    method: "get",
    params: query,
  });
}
// 获取监测活动子类型
export function getActivityType(query) {
  return request({
    url:
      import.meta.env.VITE_REQUEST_PREFIX +
      "/rocketapi/schedu/genericmgr/getActivityType",
    method: "get",
    params: query,
  });
}
// 获取监测活动扩展字段
export function getActivityTypeField(query) {
  return request({
    url:
      import.meta.env.VITE_REQUEST_PREFIX +
      "/rocketapi/schedu/genericmgr/getActivityTypeField",
    method: "get",
    params: query,
  });
}
// 计划分类
export function getPlanCategory(query) {
  return request({
    url:
      import.meta.env.VITE_REQUEST_PREFIX +
      "/rocketapi/schedu/genericmgr/getPlanCategory",
    method: "get",
    params: query,
  });
}
// 获取片区树
export function getPackageTreeInfo(query) {
  return request({
    url:
      import.meta.env.VITE_REQUEST_PREFIX +
      "/rocketapi/schedu/genericmgr/getPackageTreeInfo",
    method: "get",
    params: query,
  });
}
// 获取片区树 站点
export function getRegionTreeInfo(query) {
  return request({
    url:
      import.meta.env.VITE_REQUEST_PREFIX +
      "/rocketapi/schedu/genericmgr/getRegionTreeInfo",
    method: "get",
    params: query,
  });
}

/**
 * 文件上传
 * @param {*} formData  files  文件
 * @returns
 */
export function fileUpload(formData) {
  return request({
    url: "/file/upload",
    method: "post",
    data: formData,
  });
}
