import request from "@/utils/request";

/**
 * 4.1.1 算法列表查询
 * @param {Object} query - 查询条件参数对象
 * @returns {Promise} 请求结果的 Promise 对象
 */
export function qryAlgorithmList(query) {
  return request({
    url: `${
      import.meta.env.VITE_REQUEST_PREFIX
    }/rocketapi/schedu/dispatchrulemgr/qryAlgorithmList`,
    method: "get",
    params: query,
  });
}

/**
 * 4.1.2 算法新增/编辑
 * @param {Object} data - 要提交的算法数据
 * @returns {Promise} 请求结果的 Promise 对象
 */
export function saveAlgorithmInfo(data) {
  return request({
    url: `${
      import.meta.env.VITE_REQUEST_PREFIX
    }/rocketapi/schedu/dispatchrulemgr/saveAlgorithmInfo`,
    method: "post",
    data: data,
  });
}

/**
 * 4.1.3 算法删除
 * @param {Object} query - 查询条件参数对象
 * @returns {Promise} 请求结果的 Promise 对象
 */
export function delAlgorithmInfo(query) {
  return request({
    url: `${
      import.meta.env.VITE_REQUEST_PREFIX
    }/rocketapi/schedu/dispatchrulemgr/delAlgorithmInfo`,
    method: "get",
    params: query,
  });
}

/**
 * 4.2.1 算法调用情况查询
 * @param {Object} query - 查询条件参数对象
 * @returns {Promise} 请求结果的 Promise 对象
 */
export function qryAlgorithmCallList(query) {
  return request({
    url: `${
      import.meta.env.VITE_REQUEST_PREFIX
    }/rocketapi/schedu/dispatchrulemgr/qryAlgorithmCallList`,
    method: "get",
    params: query,
  });
}

/**
 * 4.2.2 算法调用情况导出
 * @param {Object} query - 查询条件参数对象
 * @returns {Promise} 请求结果的 Promise 对象
 */
export function exportAlgorithmCall(query) {
  return request({
    url: `${
      import.meta.env.VITE_REQUEST_PREFIX
    }/rocketapi/schedu/dispatchrulemgr/exportAlgorithmCall`,
    method: "get",
    params: query,
    responseType: "blob",
  });
}

/**
 * 4.2.3 算法调用详细列表查询
 * @param {Object} query - 查询条件参数对象
 * @returns {Promise} 请求结果的 Promise 对象
 */
export function qryAlgorithmCallDetailList(query) {
  return request({
    url: `${
      import.meta.env.VITE_REQUEST_PREFIX
    }/rocketapi/schedu/dispatchrulemgr/qryAlgorithmCallDetailList`,
    method: "get",
    params: query,
  });
}

/**
 * 4.3.1 算法规则库查询
 * @param {Object} query - 查询条件参数对象
 * @returns {Promise} 请求结果的 Promise 对象
 */
export function qryAlgorithmLibrary(query) {
  return request({
    url: `${
      import.meta.env.VITE_REQUEST_PREFIX
    }/rocketapi/schedu/dispatchrulemgr/qryAlgorithmLibrary`,
    method: "get",
    params: query,
  });
}

/**
 * 4.3.2 算法规则新增/编辑
 * @param {Object} data - 要提交的算法规则数据
 * @returns {Promise} 请求结果的 Promise 对象
 */
export function saveAlgorithmLibrary(data) {
  return request({
    url: `${
      import.meta.env.VITE_REQUEST_PREFIX
    }/rocketapi/schedu/dispatchrulemgr/saveAlgorithmLibrary`,
    method: "post",
    data: data,
  });
}

/**
 * 4.3.3 算法规则删除
 * @param {Object} query - 查询条件参数对象
 * @returns {Promise} 请求结果的 Promise 对象
 */
export function delAlgorithmLibrary(query) {
  return request({
    url: `${
      import.meta.env.VITE_REQUEST_PREFIX
    }/rocketapi/schedu/dispatchrulemgr/delAlgorithmLibrary`,
    method: "get",
    params: query,
  });
}

/**
 * 4.3.4 算法规则库导出
 * @param {Object} query - 查询条件参数对象
 * @returns {Promise} 请求结果的 Promise 对象
 */
export function exportAlgorithmLibrary(query) {
  return request({
    url: `${
      import.meta.env.VITE_REQUEST_PREFIX
    }/rocketapi/schedu/dispatchrulemgr/exportAlgorithmLibrary`,
    method: "get",
    params: query,
    responseType: "blob",
  });
}
