import request from "@/utils/request";

/**
 * 5.1.1 【调度计划分配】查询计划列表
 */
export function qryDispatchtaskPlanList(query) {
  return request({
    url: `${
      import.meta.env.VITE_REQUEST_PREFIX
    }/rocketapi/schedu/dispatchtaskmgr/qryDispatchtaskPlanList`,
    method: "get",
    params: query,
  });
}

/**
 * 5.1.2 【调度计划分配】手工分配任务
 */
export function manualDispatchPlan(data) {
  return request({
    url: `${
      import.meta.env.VITE_REQUEST_PREFIX
    }/rocketapi/schedu/dispatchtaskmgr/manualDispatchPlan`,
    method: "post",
    data,
  });
}

/**
 * 5.1.3 【调度计划分配】立刻调用算法分配任务
 */
export function callDispatchPlan(query) {
  return request({
    url: `${
      import.meta.env.VITE_REQUEST_PREFIX
    }/rocketapi/schedu/dispatchtaskmgr/callDispatchPlan`,
    method: "get",
    params: query,
  });
}

/**
 * 5.1.4 【调度计划分配】查询手工分配任务的资源【人员】列表
 */
export function qryManualDispatchResList(query) {
  return request({
    url: `${
      import.meta.env.VITE_REQUEST_PREFIX
    }/rocketapi/schedu/dispatchtaskmgr/qryManualDispatchResList`,
    method: "get",
    params: query,
  });
}

/**
 * 5.2.1 【任务调整与下发】任务列表
 */
export function qryTaskList(query) {
  return request({
    url: `${
      import.meta.env.VITE_REQUEST_PREFIX
    }/rocketapi/schedu/dispatchtaskmgr/qryTaskList`,
    method: "get",
    params: query,
  });
}

/**
 * 5.2.1 【任务调整与下发】任务详情
 */
export function qryTaskDetail(query) {
  return request({
    url: `${
      import.meta.env.VITE_REQUEST_PREFIX
    }/rocketapi/schedu/dispatchtaskmgr/qryTaskDetail`,
    method: "get",
    params: query,
  });
}

/**
 * 5.2.2 【任务调整与下发】任务调整
 */
export function updateTaskInfo(data) {
  return request({
    url: `${
      import.meta.env.VITE_REQUEST_PREFIX
    }/rocketapi/schedu/dispatchtaskmgr/updateTaskInfo`,
    method: "post",
    data,
  });
}

/**
 * 5.2.3 【任务调整与下发】任务推送活动系统
 */
export function pushActivity(data) {
  return request({
    url: `${
      import.meta.env.VITE_REQUEST_PREFIX
    }/rocketapi/schedu/dispatchtaskmgr/pushActivity`,
    method: "post",
    data,
  });
}

/**
 * 5.3.1 【重要任务审核】重要任务任务列表
 */
export function qryImportantTaskList(query) {
  return request({
    url: `${
      import.meta.env.VITE_REQUEST_PREFIX
    }/rocketapi/schedu/dispatchtaskmgr/qryImportantTaskList`,
    method: "get",
    params: query,
  });
}

/**
 * 5.3.2 【重要任务审核】任务审核
 */
export function verifyTaskInfo(data) {
  return request({
    url: `${
      import.meta.env.VITE_REQUEST_PREFIX
    }/rocketapi/schedu/dispatchtaskmgr/verifyTaskInfo`,
    method: "post",
    data,
  });
}

/**
 * 5.4.1 【调度任务跟踪】任务计划工单调整【并同步活动系统】
 * （接口未提供实现，预留）
 */
export function updateTaskPlan(query) {
  return request({
    url: `${
      import.meta.env.VITE_REQUEST_PREFIX
    }/rocketapi/schedu/dispatchtaskmgr/updateTaskPlan`,
    method: "get",
    params: query,
  });
}

/**
 * 5.4.2 【调度任务跟踪】任务计划打回【回归计划池】
 */
export function rebackPlanPool(data) {
  return request({
    url: `${
      import.meta.env.VITE_REQUEST_PREFIX
    }/rocketapi/schedu/dispatchtaskmgr/rebackPlanPool`,
    method: "post",
    data,
  });
}

/**
 * 5.5.1 【可视化】站点任务统计信息
 */
export function qryTaskStaticByPoint(query) {
  return request({
    url: `${
      import.meta.env.VITE_REQUEST_PREFIX
    }/rocketapi/schedu/dispatchtaskmgr/qryTaskStaticByPoint`,
    method: "get",
    params: query,
  });
}

/**
 * 5.5.2 【可视化】站点任务列表
 */
export function qryTaskListByPoint(query) {
  return request({
    url: `${
      import.meta.env.VITE_REQUEST_PREFIX
    }/rocketapi/schedu/dispatchtaskmgr/qryTaskListByPoint`,
    method: "get",
    params: query,
  });
}

/**
 * 5.5.3 【可视化】站点工作日历
 */
export function qryPointWorkCalendar(query) {
  return request({
    url: `${
      import.meta.env.VITE_REQUEST_PREFIX
    }/rocketapi/schedu/dispatchtaskmgr/qryPointWorkCalendar`,
    method: "get",
    params: query,
  });
}

/**
 * 5.5.4 【可视化】站点运维公司工作日历
 */
export function qryCompanyWorkCalendar(query) {
  return request({
    url: `${
      import.meta.env.VITE_REQUEST_PREFIX
    }/rocketapi/schedu/dispatchtaskmgr/qryCompanyWorkCalendar`,
    method: "get",
    params: query,
  });
}

/**
 * 5.5.5 【可视化】站点运维任务趋势
 */
export function qryTaskTrend(query) {
  return request({
    url: `${
      import.meta.env.VITE_REQUEST_HIGH_PREFIX
    }/dispatchEfficiencymgr/qryTaskTrend`,
    method: "get",
    params: query,
  });
}

/**
 * 5.5.6 【可视化】站点运维任务分析
 */
export function qryTaskStatic(query) {
  return request({
    url: `${
      import.meta.env.VITE_REQUEST_HIGH_PREFIX
    }/dispatchEfficiencymgr/qryTaskStatic`,
    method: "get",
    params: query,
  });
}

/**
 * 5.5.7 【可视化】站点运维计划类型统计
 */
export function qryPlanTypeStatic(query) {
  return request({
    url: `${
      import.meta.env.VITE_REQUEST_HIGH_PREFIX
    }/dispatchEfficiencymgr/qryPlanTypeStatic`,
    method: "get",
    params: query,
  });
}

/**
 * 5.5.8 【可视化】站点运维计划类型统计
 */
export function qryMaintenanceStatic(query) {
  return request({
    url: `${
      import.meta.env.VITE_REQUEST_HIGH_PREFIX
    }/dispatchEfficiencymgr/qryMaintenanceStatic`,
    method: "get",
    params: query,
  });
}

/**
 * 5.5.9 【可视化】站点运维计划类型列表
 */
export function qryMaintenanceList(query) {
  return request({
    url: `${
      import.meta.env.VITE_REQUEST_HIGH_PREFIX
    }/dispatchEfficiencymgr/qryMaintenanceList`,
    method: "get",
    params: query,
  });
}

/**
 * 5.5.9 【可视化】获取运维单位树
 */
export function getRegionMaintainUnitTreeInfo(query) {
  return request({
    url: `${
      import.meta.env.VITE_REQUEST_HIGH_PREFIX
    }/genericmgr/getRegionMaintainUnitTreeInfo`,
    method: "get",
    params: query,
  });
}
