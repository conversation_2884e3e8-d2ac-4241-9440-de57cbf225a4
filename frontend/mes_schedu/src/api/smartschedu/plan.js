import request from "@/utils/request";

/**
 * 查询计划规则列表
 * @param {Object} query - 查询条件参数对象
 * @returns {Promise} 请求结果的 Promise 对象
 */
export function qryPlanRule(query) {
  return request({
    url: `${
      import.meta.env.VITE_REQUEST_PREFIX
    }/rocketapi/schedu/planmgr/qryPlanRule`,
    method: "get",
    params: query,
  });
}

/**
 * 查询计划审批列表
 * @param {Object} query - 查询条件参数对象
 * @returns {Promise} 请求结果的 Promise 对象
 */
export function qryPlanApprovalList(query) {
  return request({
    url: `${
      import.meta.env.VITE_REQUEST_PREFIX
    }/rocketapi/schedu/planmgr/qryPlanApprovalList`,
    method: "get",
    params: query,
  });
}

/**
 * 查询计划信息列表
 * @param {Object} query - 查询条件参数对象
 * @returns {Promise} 请求结果的 Promise 对象
 */
export function qryPlanList(query) {
  return request({
    url: `${
      import.meta.env.VITE_REQUEST_PREFIX
    }/rocketapi/schedu/planmgr/qryPlanList`,
    method: "get",
    params: query,
  });
}

/**
 * 保存计划规则
 * @param {Object} data - 要提交的计划规则数据
 * @returns {Promise} 请求结果的 Promise 对象
 */
export function savePlanRule(data) {
  return request({
    url: `${
      import.meta.env.VITE_REQUEST_PREFIX
    }/rocketapi/schedu/planmgr/savePlanRule`,
    method: "post",
    data: data,
  });
}

/**
 * 批量保存计划规则
 * @param {Object} data - 要提交的计划规则数据
 * @returns {Promise} 请求结果的 Promise 对象
 */
export function batchSavePlanRule(data) {
  return request({
    url: `${
      import.meta.env.VITE_REQUEST_HIGH_PREFIX
    }/planmgr/batchSavePlanRule`,
    method: "post",
    data: data,
  });
}

/**
 * 更新计划规则
 * @param {Object} data - 要提交的计划规则数据
 * @returns {Promise} 请求结果的 Promise 对象
 */
export function updatePlanRule(data) {
  return request({
    url: `${
      import.meta.env.VITE_REQUEST_HIGH_PREFIX
    }/planmgr/updatePlanRule`,
    method: "post",
    data: data,
  });
}

/**
 * 保存计划信息
 * @param {Object} data - 要提交的计划信息数据
 * @returns {Promise} 请求结果的 Promise 对象
 */
export function savePlanInfo(data) {
  return request({
    url: `${
      import.meta.env.VITE_REQUEST_PREFIX
    }/rocketapi/schedu/planmgr/savePlanInfo`,
    method: "post",
    data: data,
  });
}

/**
 * 审核计划信息
 * @param {Object} data - 审核操作所需的数据（如 planId、status 等）
 * @returns {Promise} 请求结果的 Promise 对象
 */
export function verifyPlanInfo(data) {
  return request({
    url: `${
      import.meta.env.VITE_REQUEST_PREFIX
    }/rocketapi/schedu/planmgr/verifyPlanInfo`,
    method: "post",
    data: data,
  });
}

/**
 * 审核计划规则
 * @param {Object} data - 审核操作所需的数据（如 ruleId、status 等）
 * @returns {Promise} 请求结果的 Promise 对象
 */
export function verifyPlanRule(data) {
  return request({
    url: `${
      import.meta.env.VITE_REQUEST_PREFIX
    }/rocketapi/schedu/planmgr/verifyPlanRule`,
    method: "post",
    data: data,
  });
}

/**
 * 删除计划规则
 * @param {string|number} id - 要删除的计划规则 ID
 * @returns {Promise} 请求结果的 Promise 对象
 */
export function delPlanRule(id) {
  return request({
    url: `${
      import.meta.env.VITE_REQUEST_PREFIX
    }/rocketapi/schedu/planmgr/delPlanRule`,
    method: "get",
    params: { id },
  });
}

/**
 * 删除计划信息
 * @param {string|number} id - 要删除的计划信息 ID
 * @returns {Promise} 请求结果的 Promise 对象
 */
export function delPlanInfo(id) {
  return request({
    url: `${
      import.meta.env.VITE_REQUEST_PREFIX
    }/rocketapi/schedu/planmgr/delPlanInfo`,
    method: "get",
    params: { id },
  });
}

/**
 * 查询关联计划规则
 * @param {string|number} mainRuleId - 主计划规则 ID
 * @returns {Promise} 请求结果的 Promise 对象
 */
export function qryAssoPlanRule(mainRuleId) {
  return request({
    url: `${
      import.meta.env.VITE_REQUEST_PREFIX
    }/rocketapi/schedu/planmgr/qryAssoPlanRule`,
    method: "get",
    params: { mainRuleId },
  });
}

/**
 * 根据站点查询关联设备
 * @param {*} siteId
 * @returns
 */
export function qrySiteLinkDevice(siteId) {
  return request({
    url: `${
      import.meta.env.VITE_REQUEST_PREFIX
    }/rocketapi/schedu/planmgr/qrySiteLinkDevice`,
    method: "get",
    params: { siteId },
  });
}

/**
 * 查询计划详情
 * @param {*} id
 * @returns
 */
export function qryPlanDetail(id) {
  return request({
    url: `${
      import.meta.env.VITE_REQUEST_PREFIX
    }/rocketapi/schedu/planmgr/qryPlanDetail`,
    method: "get",
    params: { id },
  });
}

/**
 * 查询附件信息
 * @param {*} id
 * @returns
 */
export function qryTargetFileAttachInfo(id) {
  return request({
    url: `${
      import.meta.env.VITE_REQUEST_PREFIX
    }/rocketapi/schedu/targetmgr/qryTargetFileAttachInfo`,
    method: "get",
    params: { id },
  });
}
