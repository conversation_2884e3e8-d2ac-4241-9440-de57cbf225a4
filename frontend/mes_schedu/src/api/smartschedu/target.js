import request from "@/utils/request";

// 查询数量目标列表
export function qryNumTargetList(query) {
  return request({
    url:
      import.meta.env.VITE_REQUEST_HIGH_PREFIX + "/targetmgr/qryNumTargetList",
    method: "get",
    params: query,
  });
}

// 查询质量目标列表
export function qryQualityTargetList(query) {
  return request({
    url:
      import.meta.env.VITE_REQUEST_HIGH_PREFIX +
      "/targetmgr/qryQualityTargetList",
    method: "get",
    params: query,
  });
}

// 保存数量目标
export function saveNumTarget(data) {
  return request({
    url:
      import.meta.env.VITE_REQUEST_PREFIX +
      "/rocketapi/schedu/targetmgr/saveNumTarget",
    method: "post",
    data: data,
  });
}
// 保存质量目标
export function updateQualityTarget(data) {
  return request({
    url:
      import.meta.env.VITE_REQUEST_PREFIX +
      "/rocketapi/schedu/targetmgr/updateQualityTarget",
    method: "post",
    data: data,
  });
}
// 保存数量目标文件
export function saveNumTargetFile(data) {
  return request({
    url:
      import.meta.env.VITE_REQUEST_PREFIX +
      "/rocketapi/schedu/targetmgr/saveNumTargetFile",
    method: "post",
    data: data,
  });
}
// 删除数量目标
export function delNumTarget(id) {
  return request({
    url:
      import.meta.env.VITE_REQUEST_PREFIX +
      "/rocketapi/schedu/targetmgr/delNumTarget",
    method: "get",
    params: { id },
  });
}
// 删除数量目标文件
export function delNumTargetFile(id) {
  return request({
    url:
      import.meta.env.VITE_REQUEST_PREFIX +
      "/rocketapi/schedu/targetmgr/delNumTargetFile",
    method: "get",
    params: { id },
  });
}
// 删除质量目标文件
export function delQualityTarget(id) {
  return request({
    url:
      import.meta.env.VITE_REQUEST_PREFIX +
      "/rocketapi/schedu/targetmgr/delQualityTarget",
    method: "get",
    params: { id },
  });
}
// 查询数量目标文件列表
export function qryNumTargetFileList(params) {
  return request({
    url:
      import.meta.env.VITE_REQUEST_PREFIX +
      "/rocketapi/schedu/targetmgr/qryNumTargetFileList",
    method: "get",
    params,
  });
}

// 查询区域数量目标
export function qryCollectNumTargetList(params) {
  return request({
    url:
      import.meta.env.VITE_REQUEST_HIGH_PREFIX +
      "/targetmgr/qryCollectNumTargetList",
    method: "get",
    params,
  });
}
