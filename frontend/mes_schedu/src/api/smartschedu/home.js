import request from "@/utils/request";

/**
 * 1.1.1 【可视化】查询站点列表统计数据
 */
export function qryTaskStaticBySite(query) {
  return request({
    url: `${
      import.meta.env.VITE_REQUEST_HIGH_PREFIX
    }/dispatchtaskmgr/qryTaskStaticBySite`,
    method: "get",
    params: query,
  });
}

/**
 * 1.1.2 【可视化】查询站点列表
 */
export function qryTaskListBySite(query) {
  return request({
    url: `${
      import.meta.env.VITE_REQUEST_HIGH_PREFIX
    }/dispatchtaskmgr/qryTaskListBySite`,
    method: "get",
    params: query,
  });
}

/**
 * 1.1.3 【可视化】查询站点任务统计
 */
export function qryPeriodTaskStaticBySite(query) {
  return request({
    url: `${
      import.meta.env.VITE_REQUEST_HIGH_PREFIX
    }/dispatchtaskmgr/qryPeriodTaskStaticBySite`,
    method: "get",
    params: query,
  });
}

/**
 * 1.1.4 【可视化】查询站点任务统计甘特图
 */
export function qryCompanyWorkGanttData(query) {
  return request({
    url: `${
      import.meta.env.VITE_REQUEST_HIGH_PREFIX
    }/dispatchtaskmgr/qryCompanyWorkGanttData`,
    method: "get",
    params: query,
  });
}

/**
 * 1.1.5 【可视化】查询今日任务统计
 */
export function qryTodayTaskStatic(query) {
  return request({
    url: `${
      import.meta.env.VITE_REQUEST_HIGH_PREFIX
    }/homepage/qryTodayTaskStatic`,
    method: "get",
    params: query,
  });
}
/**
 * 1.1.5 【可视化】查询今日任务统计列表
 */
export function qryTodayTaskList(query) {
  return request({
    url: `${
      import.meta.env.VITE_REQUEST_HIGH_PREFIX
    }/homepage/qryTodayTaskList`,
    method: "get",
    params: query,
  });
}
/**
 * 1.1.5 查询数量目标管理
 */
export function qryQuantityTargetPerformance(query) {
  return request({
    url: `${
      import.meta.env.VITE_REQUEST_HIGH_PREFIX
    }/homepage/qryQuantityTargetPerformance`,
    method: "get",
    params: query,
  });
}
/**
 * 1.1.5 人员
 */
export function qryMaintainPersonStatic(query) {
  return request({
    url: `${
      import.meta.env.VITE_REQUEST_HIGH_PREFIX
    }/homepage/qryMaintainPersonStatic`,
    method: "get",
    params: query,
  });
}

/**
 *  质量目标
 */
export function qryQualityTargetPerformance(query) {
  return request({
    url: `${
      import.meta.env.VITE_REQUEST_HIGH_PREFIX
    }/homepage/qryQualityTargetPerformance`,
    method: "get",
    params: query,
  });
}
/**
 *  获取维护单位信息
 */
export function getMaintainUnitInfo(query) {
  return request({
    url: `${
      import.meta.env.VITE_REQUEST_HIGH_PREFIX
    }/genericmgr/getMaintainUnitInfo`,
    method: "get",
    params: query,
  });
}
