import auth from "@/plugins/auth";
import router, { constantRoutes, dynamicRoutes } from "@/router";
import { getRouters } from "@/api/menu";
import Layout from "@/layout/index";
import ParentView from "@/components/ParentView";
import InnerLink from "@/layout/components/InnerLink";
import { isExternal } from "../../utils/validate";

// 匹配 views 目录下所有 .vue 文件
const modules = import.meta.glob("./../../views/**/*.vue");

const appCode = import.meta.env.VITE_APP_CODE;

const usePermissionStore = defineStore("permission", {
  state: () => ({
    routes: [],
    addRoutes: [],
    defaultRoutes: [],
    topbarRouters: [],
    sidebarRouters: [],
  }),
  actions: {
    setRoutes(routes) {
      this.addRoutes = routes;
      this.routes = constantRoutes.concat(routes);
    },
    setDefaultRoutes(routes) {
      this.defaultRoutes = constantRoutes.concat(routes);
    },
    setTopbarRoutes(routes) {
      this.topbarRouters = routes;
    },
    setSidebarRouters(routes) {
      this.sidebarRouters = routes;
    },
    generateRoutes(roles) {
      return new Promise((resolve) => {
        getRouters().then((res) => {
          const allRoutes = JSON.parse(JSON.stringify(res.data));

          // 顶部导航菜单（一级）
          const topbarRoutes = collectFirstLevelRoutes(allRoutes);
          // 侧边栏菜单（当前系统子菜单）
          const sidebarRoutes = collectSidebarRoutesByName(allRoutes, appCode);

          // 注册动态权限路由
          const asyncRoutes = filterDynamicRoutes(dynamicRoutes);
          asyncRoutes.forEach((route) => {
            router.addRoute(route);
          });

          // 创建系统父级路由（不在 sidebar 展示，仅作为路由容器）
          const systemRoute = {
            path: `/${appCode}`,
            component: Layout,
            redirect:
              sidebarRoutes.length > 0 ? sidebarRoutes[0].path : `/${appCode}`,
            name: `${appCode}-root`,
            meta: { title: appCode, icon: "tree", hidden: true },
            children: sidebarRoutes,
          };

          router.addRoute(systemRoute);

          // 设置路由状态
          this.setRoutes(topbarRoutes);
          this.setSidebarRouters(sidebarRoutes);
          this.setDefaultRoutes(sidebarRoutes);
          this.setTopbarRoutes(topbarRoutes);

          resolve(topbarRoutes);
        });
      });
    },
  },
});

// 顶部菜单处理：只取一级菜单，不带 children
function collectFirstLevelRoutes(routes) {
  return routes.map((route) => {
    const newRoute = { ...route };

    if (newRoute.component) {
      if (newRoute.component === "Layout") {
        newRoute.component = Layout;
      } else if (newRoute.component === "ParentView") {
        newRoute.component = ParentView;
      } else if (newRoute.component === "InnerLink") {
        newRoute.component = InnerLink;
      } else {
        newRoute.component = loadView(newRoute.component);
      }
    }

    // 清除 children（只展示一级）
    if (newRoute.children && newRoute.path !== "/") {
      delete newRoute.children;
      delete newRoute.redirect;
    }

    return newRoute;
  });
}

// 提取当前系统的子菜单（侧边栏），并拼接系统路径前缀
function collectSidebarRoutesByName(routes, appName) {
  const appRoute = routes.find((route) => route.name === appName);

  if (!appRoute || !appRoute.children || appRoute.children.length === 0) {
    console.warn(`未找到匹配的name: ${appName}`);
    return [];
  }

  return processChildRoutes(appRoute.children, appRoute.path);
}

function processChildRoutes(children, parentPath = "") {
  return children.map((child) => {
    const newChild = { ...child };

    // 去掉开头的 /，确保是相对路径
    if (newChild.path.startsWith("/")) {
      newChild.path = newChild.path.slice(1);
    }

    // 只拼接当前父级路径，不重复拼接 appCode
    if (isExternal(newChild.path)) {
      newChild.fullPath = newChild.path;
      newChild.path = newChild.fullPath; // 用于 Vue Router 注册
    } else {
      newChild.fullPath = `${parentPath}/${newChild.path}`.replace(/\/+/g, "/");
      newChild.path = newChild.fullPath; // 用于 Vue Router 注册
    }

    // 组件处理
    if (newChild.component) {
      if (newChild.component === "ParentView") {
        newChild.component = ParentView;
      } else if (newChild.component === "InnerLink") {
        newChild.component = InnerLink;
      } else {
        newChild.component = loadView(newChild.component);
      }
    }

    // 递归处理子路由
    if (newChild.children && newChild.children.length > 0) {
      newChild.children = processChildRoutes(
        newChild.children,
        newChild.fullPath
      );
    }

    // 删除临时字段
    delete newChild.fullPath;
    console.log("newChild", newChild);
    return newChild;
  });
}

// 权限过滤
function filterDynamicRoutes(routes) {
  const res = [];
  routes.forEach((route) => {
    if (route.permissions) {
      if (auth.hasPermiOr(route.permissions)) {
        res.push(route);
      }
    } else if (route.roles) {
      if (auth.hasRoleOr(route.roles)) {
        res.push(route);
      }
    }
  });
  return res;
}

// 懒加载组件
export const loadView = (view) => {
  let res;
  for (const path in modules) {
    const dir = path.split("views/")[1].split(".vue")[0];
    if (dir === view) {
      res = () => modules[path]();
    }
  }
  return res;
};

export default usePermissionStore;
