import { defineStore } from 'pinia'

export const useUserStore = defineStore('user', {
  state: () => ({
    // 确保初始化完整的用户信息结构
    userInfo: {
      avatar: 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png',
      nickname: 'Admin',
      todoList: [], // 初始化空数组
      tasks: []
    }
  }),
  actions: {
    // 添加初始化用户信息的方法
    initUserInfo() {
      this.userInfo = {
        avatar: 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png',
        nickname: 'Admin',
        todoList: [
          { id: 1, title: '待审批流程', status: 0 },
          { id: 2, title: '月度报告', status: 0 }
        ],
        tasks: [
          { id: 1, title: '项目会议', time: '14:00' },
          { id: 2, title: '客户回访', time: '16:30' }
        ]
      }
    },
    logout() {
      this.$reset()
      this.initUserInfo() // 重置时重新初始化
      window.location.reload()
    }
  }
})