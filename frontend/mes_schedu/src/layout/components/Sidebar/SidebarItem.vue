<template>
  <div v-if="!item.hidden">
    <template
      v-if="hasOneShowingChild(item.children, item) && (!onlyOneChild.children || onlyOneChild.noShowingChildren) && !item.alwaysShow">
      <app-link v-if="onlyOneChild.meta" :to="resolvePath(onlyOneChild.path, onlyOneChild.query)">
        <el-menu-item :index="resolvePath(onlyOneChild.path)" :class="{ 'submenu-title-noDropdown': !isNest }">
          <svg-icon :icon-class="onlyOneChild.meta.icon || (item.meta && item.meta.icon)" />
          <template #title>
            <span class="menu-title">{{ onlyOneChild.meta.title }}</span>
          </template>
        </el-menu-item>
      </app-link>
    </template>

    <el-sub-menu v-else ref="subMenu" :index="resolvePath(item.path)" teleported>
      <template v-if="item.meta" #title>
        <svg-icon :icon-class="item.meta && item.meta.icon" />
        <span class="menu-title" :title="hasTitle(item.meta.title)">{{ item.meta.title }}</span>
      </template>
      <sidebar-item v-for="(child, index) in item.children" :key="child.path + index" :is-nest="true" :item="child"
        :base-path="resolvePath(child.path)" class="nest-menu" />
    </el-sub-menu>
  </div>
</template>

<script setup>
import { isExternal } from '@/utils/validate'
import AppLink from './Link'
import { getNormalPath } from '@/utils/ruoyi'

const props = defineProps({
  // route object
  item: {
    type: Object,
    required: true
  },
  isNest: {
    type: Boolean,
    default: false
  },
  basePath: {
    type: String,
    default: ''
  }
})

const onlyOneChild = ref({})
const subMenu = ref(null)

function hasOneShowingChild(children = [], parent) {
  const showingChildren = children.filter(item => !item.hidden)

  if (showingChildren.length === 1) {
    onlyOneChild.value = showingChildren[0]
    return true
  }

  if (showingChildren.length === 0) {
    onlyOneChild.value = { ...parent, path: '', noShowingChildren: true }
    return true
  }

  return false
}

function resolvePath(routePath, routeQuery) {
  if (isExternal(routePath)) {
    return routePath
  }
  if (isExternal(props.basePath)) {
    return props.basePath
  }

  // 如果 routePath 是绝对路径或已经包含 basePath，直接返回，不拼接
  if (routePath.startsWith('/') || routePath.startsWith(props.basePath)) {
    const fullPath = getNormalPath(routePath)
    if (routeQuery) {
      try {
        const query = JSON.parse(routeQuery)
        return { path: fullPath, query }
      } catch (e) {
        console.error('Failed to parse route query', routeQuery, e)
      }
    }
    return fullPath
  }

  // 正常拼接 basePath + routePath
  const fullPath = getNormalPath(props.basePath + '/' + routePath)
  if (routeQuery) {
    try {
      const query = JSON.parse(routeQuery)
      return { path: fullPath, query }
    } catch (e) {
      console.error('Failed to parse route query', routeQuery, e)
    }
  }
  return fullPath
}


function hasTitle(title) {
  return title || ''
}
</script>
