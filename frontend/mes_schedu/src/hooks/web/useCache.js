/**
 * 配置浏览器本地存储方式，支持存储对象和数组
 * 使用 web-storage-cache 库实现
 */

import WebStorageCache from 'web-storage-cache'

// 缓存键名常量（保持与TS版本一致）
export const CACHE_KEY = {
  // 用户相关
  ROLE_ROUTERS: 'roleRouters',
  USER: 'user',
  // 系统设置
  IS_DARK: 'isDark',
  LANG: 'lang',
  THEME: 'theme',
  LAYOUT: 'layout',
  DICT_CACHE: 'dictCache',
  // 登录相关
  LoginForm: 'loginForm',
  TenantId: 'tenantId'
}

/**
 * 创建缓存实例
 * @param {'localStorage' | 'sessionStorage'} type 存储类型
 * @returns {Object} 包含 wsCache 实例的对象
 */
export const useCache = (type = 'localStorage') => {
  const wsCache = new WebStorageCache({
    storage: type,
    exp: Infinity // 默认永不过期（由业务代码控制过期时间）
  })

  return {
    wsCache
  }
}

/**
 * 清理用户相关缓存（退出登录时使用）
 * 注意：保留登录表单信息
 */
export const deleteUserCache = () => {
  const { wsCache } = useCache()
  
  // 删除用户权限相关缓存
  wsCache.delete(CACHE_KEY.USER)
  wsCache.delete(CACHE_KEY.ROLE_ROUTERS)
  
  // 保留以下缓存不清除：
  // 1. CACHE_KEY.LoginForm (登录表单)
  // 2. CACHE_KEY.TenantId (租户ID)
  // 3. 系统设置相关（主题/语言等）
}