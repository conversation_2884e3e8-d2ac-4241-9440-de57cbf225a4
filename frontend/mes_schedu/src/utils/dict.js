import useDictStore from "@/store/modules/dict";
import { getDicts } from "@/api/system/dict/data";
import { getBusDicts } from "@/api/busDict";

/**
 * 获取字典数据
 */
export function useDict(...args) {
  const res = ref({});
  return (() => {
    args.forEach((dictType, index) => {
      res.value[dictType] = [];
      const dicts = useDictStore().getDict(dictType);
      if (dicts) {
        res.value[dictType] = dicts;
      } else {
        getDicts(dictType).then((resp) => {
          res.value[dictType] = resp.data.map((p) => ({
            label: p.dictLabel,
            value: p.dictValue,
            elTagType: p.listClass,
            elTagClass: p.cssClass,
          }));
          useDictStore().setDict(dictType, res.value[dictType]);
        });
      }
    });
    return toRefs(res.value);
  })();
}

/**
 * 获取业务字典数据
 */
export async function useBusDict(...args) {
  const res = ref({});

  // Wrap the IIFE with async and await the asynchronous data fetching
  await (async () => {
    for (const dictType of args) {
      res.value[dictType] = [];
      const dicts = useDictStore().getDict(dictType);

      if (dicts) {
        res.value[dictType] = dicts;
      } else {
        try {
          const resp = await getBusDicts(dictType);
          res.value[dictType] = resp.data.map((p) => ({
            label: p.dictValue,
            value: p.dictCode,
            elTagType: p.listClass,
            elTagClass: p.cssClass,
          }));
          useDictStore().setDict(dictType, res.value[dictType]);
        } catch (error) {
          console.error(`Failed to fetch dictionary for ${dictType}:`, error);
        }
      }
    }
  })();

  return toRefs(res.value);
}
