// auth.js
import Cookies from 'js-cookie'

const TokenKey = 'Admin-Token'

const SSO_TokenKey = 'SSO-Token_SSO'

const ExpiresInKey = 'Admin-Expires-In'



// ========== Token 相关 ==========
// 获取访问令牌
// begin SSOTOKEN 
export function getAccessToken(){
  return Cookies.get(SSO_TokenKey)
}


export function setAccessToken(token){
  return Cookies.set(SSO_TokenKey, token)
}

// end SSOTOKEN 


export function getToken() {
  return Cookies.get(TokenKey)
}

export function setToken(token) {
  return Cookies.set(TokenKey, token)
}

export function removeToken() {
  return Cookies.remove(TokenKey)
}

export function getExpiresIn() {
  return Cookies.get(ExpiresInKey) || -1
}

export function setExpiresIn(time) {
  return Cookies.set(ExpiresInKey, time)
}

export function removeExpiresIn() {
  return Cookies.remove(ExpiresInKey)
}
