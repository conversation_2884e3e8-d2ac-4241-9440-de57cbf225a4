import IdbaseAuth from '@idbase/idbase-auth'
import IdbaseVue from '@idbase/idbase-vue'
import ssoConfig, { SSO_Callback } from './config'
import { ssoEvents, restoreOriginalUri } from './event'

const idbaseAuth = new IdbaseAuth(ssoConfig)

export const setupSso = (app) => {
  app.use(IdbaseVue, {
    oAuth: idbaseAuth,
    ssoEvents: ssoEvents,
    restoreOriginalUri: restoreOriginalUri
  })
}

export { SSO_Callback, ssoEvents, restoreOriginalUri }

export default ssoConfig