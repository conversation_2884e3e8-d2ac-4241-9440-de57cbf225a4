import router from '@/router'
import { ssoLogin } from '@/api/login'
import { getAccessToken,setAccessToken, setToken ,setExpiresIn} from '@/utils/auth'
import { CACHE_KEY, useCache } from '@/hooks/web/useCache'
import Cookies from 'js-cookie'

const { wsCache } = useCache()

const localAuthenticated = async () => {
  
  const token = getAccessToken()
  return !!token
}

const localUser = () => {
  const userInfo = wsCache.get(CACHE_KEY.USER)?.user ?? {};
  const loginForm = wsCache.get(CACHE_KEY.LoginForm) ?? {};
  console.log('userInfo', { userId: userInfo.id, username: loginForm.username })
  return { userId: userInfo.id, username: loginForm.username }
}

const onOAuthRequired = (idbaseAuth) => {
  
}

const onOAuthHandled = ({ idbaseAuth, tokens }) => {
  return new Promise((resolve, reject) => {
    
    if (!tokens.idToken.claims.local_username) {
      reject('未绑定本地用户')
      const originUri = idbaseAuth.getOriginalUri()
      router.replace({
        path: '/login',
        query: { redirect: originUri }
      })
      return
    }

    const config = {
      headers: {
        Authorization: `bearer ${tokens.idToken.idToken}`,
        isToken:false
      }
    }
    Cookies.remove("username")
    Cookies.remove("password")
    Cookies.remove('rememberMe')
    setAccessToken(tokens.idToken.idToken)// 存储在本地
    ssoLogin(config).then(response => {
      setToken(response.data.access_token)
      setExpiresIn(response.data.expires_in)
      
      resolve('')
    }).catch(() => {
      reject('获取本地用户token异常')
    })
  })
}

const restoreOriginalUri = async (idbaseAuth, originalUri) => {
  router.replace({ path: originalUri })
}

const ssoEvents = {
  localAuthenticated: localAuthenticated,
  localUser: localUser,
  onOAuthRequired: onOAuthRequired,
  onOAuthHandled: onOAuthHandled
}

export { ssoEvents, restoreOriginalUri }