const CLIENT_ID = 'cnemc006'
const ISSUER = 'https://idaut.cnemc.cn'
const BASEPATH = ''
const SSO_Callback = '/sso/callback'
const REDIRECT_URI = `${window.location.origin}${BASEPATH}${SSO_Callback}`

const ssoConfig = {
  clientId: CLIENT_ID,
  issuer: ISSUER,
  redirectUri: REDIRECT_URI,
  scopes: ['openid', 'profile'],
  tokenManager: {
    autoRenew: false
  }
}

export { SSO_Callback }

export default ssoConfig