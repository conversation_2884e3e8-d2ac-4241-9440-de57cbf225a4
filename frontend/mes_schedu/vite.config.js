import { defineConfig, loadEnv } from 'vite';
import path from 'path';
import createVitePlugins from './vite/plugins';

//const baseUrl = 'http://smart.huqingyun.top' // 后端接口

const baseUrl = 'http://10.10.193.159:19098'; // 后端接口
const port = 5175;

export default defineConfig(({ mode, command }) => {
  const env = loadEnv(mode, process.cwd(), '');
  const { VITE_APP_ENV } = env;

  return {
    // 部署生产环境和开发环境下的URL
    base: VITE_APP_ENV === 'production' ? '/smartschedu' : '/smartschedu',

    // 插件配置
    plugins: createVitePlugins(env, command === 'build'),

    // 路径别名配置
    resolve: {
      alias: {
        // 确保路径映射正确
        '@idbase/idbase-auth': path.resolve(
          __dirname,
          'node_modules/@idbase/idbase-auth'
        ),
        '@idbase/idbase-vue': path.resolve(
          __dirname,
          'node_modules/@idbase/idbase-vue'
        ),
        '~': path.resolve(__dirname, './'),
        '@': path.resolve(__dirname, './src'),
      },
      extensions: ['.mjs', '.js', '.ts', '.jsx', '.tsx', '.json', '.vue'],
      define: {
        // 兼容老项目中的 process.env 写法（仅开发环境）
        ...(mode === 'development'
          ? {
              'process.env': {
                NODE_ENV: JSON.stringify(mode),
                VITE_APP_BASE_API: JSON.stringify(env.VITE_APP_BASE_API),
              },
            }
          : {}),
      },
    },

    // 打包配置
    build: {
      sourcemap: command === 'build' ? false : 'inline',
      outDir: 'smartschedu',
      assetsDir: 'assets',
      chunkSizeWarningLimit: 2000,
      rollupOptions: {
        output: {
          chunkFileNames: 'static/js/[name]-[hash].js',
          entryFileNames: 'static/js/[name]-[hash].js',
          assetFileNames: 'static/[ext]/[name]-[hash].[ext]',
        },
      },
    },

    // 开发服务器配置
    server: {
      port: port,
      host: true,
      open: true,
      proxy: {
        // API 代理配置
        '/dev-api': {
          target: baseUrl,
          changeOrigin: true,
          configure: (proxy, options) => {
            console.log('[PROXY] API 代理已配置:', options.target);

            // 监听代理请求事件
            proxy.on('proxyReq', (proxyReq, req, res) => {
              const originalUrl = req.originalUrl || req.url;

              if (!originalUrl) {
                console.warn('[PROXY] 缺少原始URL:', req.method);
                return;
              }

              // 使用 URL 模块处理路径
              const targetUrl = new URL(
                originalUrl.replace('/dev-api', ''),
                options.target
              ).href;

              console.log(
                `[PROXY] ${req.method} ${originalUrl} => ${targetUrl}`
              );

              // 调试请求头
              console.debug('[PROXY] 请求头:', req.headers);
            });

            // 添加错误处理
            proxy.on('error', (err, req, res) => {
              console.error('[PROXY] 代理错误:', err.message);
            });

            // 监听代理响应
            proxy.on('proxyRes', (proxyRes, req, res) => {
              console.log(
                `[PROXY] 响应: ${req.method} ${req.originalUrl || req.url} ${
                  proxyRes.statusCode
                }`
              );
            });
          },
          rewrite: path => {
            console.log('[PROXY] 路径重写前:', path);
            const newPath = path.replace(/^\/dev-api/, '');
            console.log('[PROXY] 路径重写后:', newPath);
            return newPath;
          },
        },

        // SpringDoc API文档代理
        '^/v3/api-docs/(.*)': {
          target: baseUrl,
          changeOrigin: true,
          configure: proxy => {
            proxy.on('proxyReq', (proxyReq, req) => {
              console.log(
                `[PROXY:DOCS] ${req.method} ${req.url} => ${baseUrl}${req.url}`
              );
            });
          },
        },
      },

      fs: {
        allow: ['.'],
      },
    },

    // ESBuild 配置
    esbuild: {
      jsxFactory: 'h',
      jsxFragment: 'Fragment',
    },

    // CSS 配置
    css: {
      postcss: {
        plugins: [
          {
            postcssPlugin: 'internal:charset-removal',
            AtRule: {
              charset: atRule => {
                if (atRule.name === 'charset') {
                  atRule.remove();
                }
              },
            },
          },
        ],
      },
    },

    // 依赖预构建配置
    optimizeDeps: {
      include: [
        'element-plus',
        'element-plus/es/locale/lang/zh-cn',
        'js-cookie',
        '@idbase/idbase-auth',
        '@idbase/idbase-vue',
      ],
      exclude: ['@idbase/idbase-auth', '@idbase/idbase-vue'],
    },
  };
});
