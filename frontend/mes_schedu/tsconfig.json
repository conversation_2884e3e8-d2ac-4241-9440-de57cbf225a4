{
  "compilerOptions": {
    "target": "ESNext",
    "module": "ESNext",
    "moduleResolution": "Node",
    "strict": false, // 可选：设为 true 启用严格类型检查
    "jsx": "preserve",
    "sourceMap": false,
    "resolveJsonModule": true,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "baseUrl": ".",
    "paths": {
      "@/*": ["src/*"]
    },
    "types": [
      "@intlify/unplugin-vue-i18n/types",
      "vite/client",
      "element-plus/global",
      "vite-plugin-svg-icons/client"
    ],
    "lib": ["ESNext", "DOM"],
    "outDir": "target", // 请保留这个属性，防止tsconfig.json文件报错
  },
  "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.js", "src/**/*.jsx"],
  "exclude": ["node_modules", "dist","target",]
}